import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface RoleSelectorProps {
  currentRole: 'sales' | 'designer' | 'supervisor' | 'manager' | 'admin';
  onRoleChange: (newRole: string) => void;
  disabled?: boolean;
  showBadge?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const ROLE_COLORS = {
  sales: 'bg-blue-100 text-blue-800',
  designer: 'bg-purple-100 text-purple-800',
  supervisor: 'bg-green-100 text-green-800',
  manager: 'bg-red-100 text-red-800',
  admin: 'bg-orange-100 text-orange-800',
};

const ROLE_LABELS = {
  sales: 'Sales',
  designer: 'Designer',
  supervisor: 'Supervisor',
  manager: 'Manager',
  admin: 'Admin',
};

const RoleSelector = ({ 
  currentRole, 
  onRoleChange, 
  disabled = false, 
  showBadge = false,
  size = 'md' 
}: RoleSelectorProps) => {
  const sizeClasses = {
    sm: 'w-24 h-8 text-xs',
    md: 'w-32 h-10 text-sm',
    lg: 'w-40 h-12 text-base',
  };

  if (showBadge) {
    return (
      <Badge className={ROLE_COLORS[currentRole]}>
        {ROLE_LABELS[currentRole]}
      </Badge>
    );
  }

  return (
    <Select
      value={currentRole}
      onValueChange={onRoleChange}
      disabled={disabled}
    >
      <SelectTrigger className={sizeClasses[size]}>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="sales">Sales</SelectItem>
        <SelectItem value="designer">Designer</SelectItem>
        <SelectItem value="supervisor">Supervisor</SelectItem>
        <SelectItem value="manager">Manager</SelectItem>
        <SelectItem value="admin">Admin</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default RoleSelector;
